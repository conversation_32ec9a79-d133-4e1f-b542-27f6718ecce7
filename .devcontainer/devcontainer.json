{
  "name": "drops-relation-latest",
  "dockerComposeFile": "docker-compose.yml",
  "service": "dev-latest",
  // "service": "dev-1.15",
  // "service": "dev-1.14",
  // "service": "dev-1.13",
  "runServices": [
    "dev-latest"
  ],
  "workspaceFolder": "/workspace/drops-relation",
  "features": {
    "ghcr.io/devcontainers/features/github-cli": {},
    "ghcr.io/nils-geistmann/devcontainers-features/zsh": {},
    "ghcr.io/rocker-org/devcontainer-features/apt-packages": {
      "packages": "inotify-tools git postgresql-client-16 libpq-dev sqlite3"
    }
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "sleistner.vscode-fileutils",
        "kahole.magit",
        "JakeBecker.elixir-ls",
        "Augment.vscode-augment",
        "GitHub.vscode-pull-request-github",
        "GitHub.copilot"
      ],
      "settings": {
        "terminal.integrated.shell.linux": "/usr/local/bin/zsh",
        "editor.formatOnSave": true
      },
      "postCreateCommand": "cd ~/dotfiles && git pull --rebase && ./install.sh"
    }
  }
}
