defmodule Mix.Tasks.Drops.Relation.RefreshCacheTest do
  use ExUnit.Case, async: false

  alias Mix.Tasks.Drops.Relation.RefreshCache

  describe "run/1" do
    test "shows help when --help is provided" do
      result = RefreshCache.run(["--help"])
      assert result == :ok
    end

    test "handles no repositories gracefully" do
      # Test with no repos configured and no arguments
      # This should return an error about no repositories found
      original_repos = Application.get_env(:drops_relation, :ecto_repos, [])

      try do
        Application.put_env(:drops_relation, :ecto_repos, [])
        result = RefreshCache.run([])
        assert result == {:error, :no_repos}
      after
        Application.put_env(:drops_relation, :ecto_repos, original_repos)
      end
    end

    test "task module exists and is loadable" do
      # Basic smoke test to ensure the module compiles and loads
      assert Code.ensure_loaded?(RefreshCache)
      assert function_exported?(RefreshCache, :run, 1)
    end
  end
end
